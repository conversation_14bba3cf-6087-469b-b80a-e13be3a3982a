#!/bin/bash

# New-API 综合管理脚本
# 版本: 1.0
# 作者: AI Assistant
# 描述: 用于管理 new-api 项目的启动、停止、备份、更新等操作

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="${SCRIPT_DIR}/scripts"
PROJECT_NAME="new-api"
COMPOSE_FILE="${SCRIPT_DIR}/docker-compose.yml"
BACKUP_DIR="${SCRIPT_DIR}/backup"
LOG_FILE="${SCRIPT_DIR}/logs/manage.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

log_info() {
    log "INFO" "$*"
    printf "\033[0;32m[INFO]\033[0m %s\n" "$*"
}

log_warn() {
    log "WARN" "$*"
    printf "\033[1;33m[WARN]\033[0m %s\n" "$*"
}

log_error() {
    log "ERROR" "$*"
    printf "\033[0;31m[ERROR]\033[0m %s\n" "$*"
}

# 检查依赖
check_dependencies() {
    local deps=("docker" "docker-compose")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "$dep 未安装，请先安装 $dep"
            exit 1
        fi
    done
}

# 创建必要的目录
create_directories() {
    mkdir -p "${SCRIPTS_DIR}" "${BACKUP_DIR}" "$(dirname "${LOG_FILE}")"
}

# 显示交互式菜单
show_interactive_menu() {
    clear
    printf "\033[0;34m╔══════════════════════════════════════════════════════════════╗\033[0m\n"
    printf "\033[0;34m║                    New-API 综合管理脚本                        ║\033[0m\n"
    printf "\033[0;34m║                         v1.0                                  ║\033[0m\n"
    printf "\033[0;34m╚══════════════════════════════════════════════════════════════╝\033[0m\n"
    printf "\n"
    printf "\033[0;32m请选择要执行的操作:\033[0m\n"
    printf "\n"
    printf "\033[1;33m📋 服务管理\033[0m\n"
    printf "  \033[0;32m1)\033[0m 启动服务\n"
    printf "  \033[0;32m2)\033[0m 停止服务\n"
    printf "  \033[0;32m3)\033[0m 重启服务\n"
    printf "  \033[0;32m4)\033[0m 查看服务状态\n"
    printf "  \033[0;32m5)\033[0m 查看服务日志\n"
    printf "\n"
    printf "\033[1;33m🔄 更新管理\033[0m\n"
    printf "  \033[0;32m6)\033[0m 更新到最新版本\n"
    printf "  \033[0;32m7)\033[0m 仅拉取最新镜像\n"
    printf "\n"
    printf "\033[1;33m💾 备份管理\033[0m\n"
    printf "  \033[0;32m8)\033[0m 手动备份数据库\n"
    printf "  \033[0;32m9)\033[0m 恢复数据库备份\n"
    printf "  \033[0;32m10)\033[0m 启用自动备份\n"
    printf "  \033[0;32m11)\033[0m 列出所有备份\n"
    printf "\n"
    printf "\033[1;33m🛠️  维护管理\033[0m\n"
    printf "  \033[0;32m12)\033[0m 清理Docker资源\n"
    printf "  \033[0;32m13)\033[0m 性能监控\n"
    printf "  \033[0;32m14)\033[0m 重置服务 \033[0;31m(危险操作)\033[0m\n"
    printf "\n"
    printf "\033[1;33mℹ️  其他选项\033[0m\n"
    printf "  \033[0;32m15)\033[0m 显示版本信息\n"
    printf "  \033[0;32m16)\033[0m 显示帮助信息\n"
    printf "  \033[0;32m0)\033[0m 退出\n"
    printf "  \033[0;32mEnter)\033[0m 退出\n"
    printf "\n"
    printf "\033[0;34m═══════════════════════════════════════════════════════════════\033[0m\n"
}

# 显示帮助信息
show_help() {
    cat << EOF
${BLUE}New-API 综合管理脚本${NC}

用法: $0 [选项]

选项:
  ${GREEN}服务管理:${NC}
    start           启动服务
    stop            停止服务
    restart         重启服务
    status          查看服务状态
    logs            查看服务日志

  ${GREEN}更新管理:${NC}
    update          拉取最新镜像并重启服务
    pull            仅拉取最新镜像

  ${GREEN}备份管理:${NC}
    backup          手动备份数据库
    restore         恢复数据库备份
    auto-backup     启用自动备份
    list-backups    列出所有备份

  ${GREEN}维护管理:${NC}
    cleanup         清理未使用的Docker资源
    reset           重置服务（危险操作）

  ${GREEN}其他:${NC}
    help            显示此帮助信息
    version         显示版本信息

示例:
  $0 start          # 启动服务
  $0 backup         # 备份数据库
  $0 update         # 更新到最新版本
  $0 status         # 查看服务状态

${YELLOW}提示: 直接运行 $0 进入交互式菜单${NC}
EOF
}

# 显示版本信息
show_version() {
    printf "\033[0;34mNew-API 管理脚本 v1.0\033[0m\n"
    printf "项目路径: %s\n" "${SCRIPT_DIR}"
    printf "Docker Compose 文件: %s\n" "${COMPOSE_FILE}"
}

# 处理交互式菜单选择
handle_menu_choice() {
    local choice=$1

    case $choice in
        1)
            log_info "启动 ${PROJECT_NAME} 服务..."
            bash "${SCRIPTS_DIR}/service.sh" start
            ;;
        2)
            log_info "停止 ${PROJECT_NAME} 服务..."
            bash "${SCRIPTS_DIR}/service.sh" stop
            ;;
        3)
            log_info "重启 ${PROJECT_NAME} 服务..."
            bash "${SCRIPTS_DIR}/service.sh" restart
            ;;
        4)
            bash "${SCRIPTS_DIR}/service.sh" status
            ;;
        5)
            printf "\033[1;33m请选择要查看的服务日志:\033[0m\n"
            printf "1) 所有服务\n"
            printf "2) new-api\n"
            printf "3) mysql\n"
            printf "4) redis\n"
            read -p "请输入选择 (1-4): " log_choice
            case $log_choice in
                1) bash "${SCRIPTS_DIR}/service.sh" logs ;;
                2) bash "${SCRIPTS_DIR}/service.sh" logs new-api ;;
                3) bash "${SCRIPTS_DIR}/service.sh" logs mysql ;;
                4) bash "${SCRIPTS_DIR}/service.sh" logs redis ;;
                *) log_error "无效选择" ;;
            esac
            ;;
        6)
            log_info "更新 ${PROJECT_NAME} 到最新版本..."
            bash "${SCRIPTS_DIR}/update.sh" update
            ;;
        7)
            log_info "拉取最新镜像..."
            bash "${SCRIPTS_DIR}/update.sh" pull
            ;;
        8)
            log_info "开始备份数据库..."
            bash "${SCRIPTS_DIR}/backup.sh" backup
            ;;
        9)
            log_info "恢复数据库备份..."
            bash "${SCRIPTS_DIR}/backup.sh" list
            echo ""
            read -p "请输入要恢复的备份文件名 (或输入 'latest' 恢复最新备份): " backup_file
            if [ -n "$backup_file" ]; then
                bash "${SCRIPTS_DIR}/backup.sh" restore "$backup_file"
            else
                log_error "未指定备份文件"
            fi
            ;;
        10)
            log_info "配置自动备份..."
            printf "\033[1;33m当前默认备份时间: 每天凌晨2点\033[0m\n"
            read -p "是否使用默认时间？(y/N): " use_default
            if [[ $use_default =~ ^[Yy]$ ]]; then
                bash "${SCRIPTS_DIR}/backup.sh" auto-backup
            else
                printf "\033[1;33m请输入cron格式的时间 (例如: '0 3 * * *' 表示每天凌晨3点):\033[0m\n"
                read -p "备份时间: " backup_time
                if [ -n "$backup_time" ]; then
                    bash "${SCRIPTS_DIR}/backup.sh" auto-backup "$backup_time"
                else
                    log_error "未指定备份时间"
                fi
            fi
            ;;
        11)
            bash "${SCRIPTS_DIR}/backup.sh" list
            ;;
        12)
            log_info "清理Docker资源..."
            bash "${SCRIPTS_DIR}/maintenance.sh" cleanup
            ;;
        13)
            bash "${SCRIPTS_DIR}/maintenance.sh" monitor
            ;;
        14)
            log_warn "重置服务（这将删除所有数据）..."
            bash "${SCRIPTS_DIR}/maintenance.sh" reset
            ;;
        15)
            show_version
            ;;
        16)
            show_help
            ;;
        0)
            log_info "退出管理脚本"
            exit 0
            ;;
        *)
            log_error "无效选择: $choice"
            return 1
            ;;
    esac
}

# 交互式菜单主循环
interactive_menu() {
    while true; do
        show_interactive_menu

        printf "\033[0;32m请输入您的选择 (0-16 或 Enter 退出): \033[0m"
        read -r choice

        # 处理空输入（Enter键）
        if [ -z "$choice" ]; then
            printf "\033[0;32m[INFO]\033[0m 退出管理脚本\n"
            exit 0
        fi

        # 验证输入是否为数字
        if ! [[ "$choice" =~ ^[0-9]+$ ]]; then
            printf "\033[0;31m错误: 请输入有效的数字\033[0m\n"
            read -p "按回车键继续..." -r
            continue
        fi

        # 处理选择
        printf "\n"
        handle_menu_choice "$choice"

        # 如果不是退出选项，等待用户确认后继续
        if [ "$choice" != "0" ]; then
            printf "\n"
            read -p "按回车键返回主菜单..." -r
        fi
    done
}

# 主函数
main() {
    # 检查依赖和创建目录
    check_dependencies
    create_directories

    # 确保子脚本存在
    if [ ! -f "${SCRIPTS_DIR}/service.sh" ]; then
        log_warn "子脚本不存在，请先运行安装脚本: ./scripts/install.sh"
        exit 1
    fi

    # 如果没有参数，启动交互式菜单
    if [ $# -eq 0 ]; then
        interactive_menu
        exit 0
    fi

    local command=$1
    shift

    case $command in
        start)
            log_info "启动 ${PROJECT_NAME} 服务..."
            bash "${SCRIPTS_DIR}/service.sh" start "$@"
            ;;
        stop)
            log_info "停止 ${PROJECT_NAME} 服务..."
            bash "${SCRIPTS_DIR}/service.sh" stop "$@"
            ;;
        restart)
            log_info "重启 ${PROJECT_NAME} 服务..."
            bash "${SCRIPTS_DIR}/service.sh" restart "$@"
            ;;
        status)
            bash "${SCRIPTS_DIR}/service.sh" status "$@"
            ;;
        logs)
            bash "${SCRIPTS_DIR}/service.sh" logs "$@"
            ;;
        update)
            log_info "更新 ${PROJECT_NAME} 到最新版本..."
            bash "${SCRIPTS_DIR}/update.sh" update "$@"
            ;;
        pull)
            log_info "拉取最新镜像..."
            bash "${SCRIPTS_DIR}/update.sh" pull "$@"
            ;;
        backup)
            log_info "开始备份数据库..."
            bash "${SCRIPTS_DIR}/backup.sh" backup "$@"
            ;;
        restore)
            log_info "开始恢复数据库..."
            bash "${SCRIPTS_DIR}/backup.sh" restore "$@"
            ;;
        auto-backup)
            log_info "配置自动备份..."
            bash "${SCRIPTS_DIR}/backup.sh" auto-backup "$@"
            ;;
        list-backups)
            bash "${SCRIPTS_DIR}/backup.sh" list "$@"
            ;;
        cleanup)
            log_info "清理Docker资源..."
            bash "${SCRIPTS_DIR}/maintenance.sh" cleanup "$@"
            ;;
        reset)
            log_warn "重置服务（这将删除所有数据）..."
            bash "${SCRIPTS_DIR}/maintenance.sh" reset "$@"
            ;;
        menu|interactive)
            interactive_menu
            ;;
        help|--help|-h)
            show_help
            ;;
        version|--version|-v)
            show_version
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
